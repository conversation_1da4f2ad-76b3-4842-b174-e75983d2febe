const api = require("binance");
const RSI = require("technicalindicators").RSI;
const request = require("request-promise");
const TelegramBot = require("node-telegram-bot-api");

const binanceWS = new api.BinanceWS(true);
const period = 14;

const opts = {
  parse_mode: "HTML",
};

const RSIData = {
  BTCUSDT: 0,
  // ETHUSDT: 0,
};

let defaultInt = 1;

const getRSI = async (pair, interval) => {
  try {
    if (pair) {
      const res = await request(
        `https://api.binance.com/api/v3/klines?symbol=${pair.toUpperCase()}&interval=${interval}m&limit=500`
      ).json();
      if (res.length > 10) {
        let values = [];
        for (var i = 0; i <= res.length - 1; i++) {
          values.push(parseFloat(res[i][4]));
        }
        let input = {
          period,
          values,
        };
        const result = RSI.calculate(input);
        return {
          price: values[values.length - 2],
          rsi: result[result.length - 2],
        };
      }
    }
    return 0;
  } catch (e) {
    console.log(e);
    return 0;
  }
};

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const updateData = async (interval) => {
  for (let key in RSIData) {
    RSIData[key] = await getRSI(key, interval);
  }
  await sleep(interval * 60 * 1000);
  updateData(interval);
};

updateData(5);

const tracker = [];

const checkPrice = (pair) => {
  const lastRSI = RSIData[pair][RSIData.length - 2];
  if (lastRSI >= 70) {
    tracker.push(lastRSI);
  }
  if (lastRSI <= 70) {
    let peak1 = tracker.max();
  }
};

const roundTo2 = (number) => {
  return Math.round(number * 100) / 100;
};
