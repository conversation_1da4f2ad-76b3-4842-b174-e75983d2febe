const api = require("binance");
const BB = require("technicalindicators").BollingerBands;
const request = require("request-promise");
const TelegramBot = require("node-telegram-bot-api");

const binanceWS = new api.BinanceWS(true);
const period = 15;

const opts = {
  parse_mode: "HTML",
};

const bollingerData = {
  BTCUSDT: 0,
  ETHUSDT: 0,
};

let defaultInt = 1;

const getBollingerBand = async (pair, interval) => {
  try {
    if (pair) {
      const res = await request(
        `https://api.binance.com/api/v3/klines?symbol=${pair.toUpperCase()}&interval=${interval}m&limit=50`
      ).json();
      if (res.length > 30) {
        let values = [];
        for (var i = res.length - 1; i > 0; i--) {
          values.push(parseFloat(res[i][4]));
        }
        let input = {
          period,
          values,
          stdDev: 2,
        };
        return BB.calculate(input)[0];
      }
    }
    return 0;
  } catch (e) {
    console.log(e);
    return 0;
  }
};

const technicalBotToken = "591628200:AAGSsNb4w2wYBVPLt2ssZLuGhLTCtP0hp2w"; // User your bot token
const channelId = -1001333970956; // Chat room id
const bot = new TelegramBot(technicalBotToken, { polling: true });

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const updateData = async (interval) => {
  for (let key in bollingerData) {
    bollingerData[key] = await getBollingerBand(key, interval);
  }
  await sleep(interval * 60 * 1000);
  updateData(interval);
};

updateData(5);

const streams = binanceWS.streams;

binanceWS.onCombinedStream(
  [streams.trade("BTCUSDT"), streams.trade("ETHUSDT")],
  (streamEvent) => {
    switch (streamEvent.stream) {
      case streams.trade("BTCUSDT"):
        checkPrice(roundTo2(streamEvent.data.price), "BTCUSDT");
        checkPriceAndBuy(roundTo2(streamEvent.data.price), "BTCUSDT");
        break;
      case streams.trade("ETHUSDT"):
        checkPrice(roundTo2(streamEvent.data.price), "ETHUSDT");
        checkPriceAndBuy(roundTo2(streamEvent.data.price), "ETHUSDT");
        break;
      default:
        break;
    }
  }
);

const checkPrice = (price, pair) => {
  const threshold = roundTo2(
    (bollingerData[pair].middle - bollingerData[pair].lower) / 2
  );
  if (
    (price <= bollingerData[pair].lower - threshold ||
      price >= bollingerData[pair].upper + threshold) &&
    !bollingerData[pair].alert
  ) {
    let bm = `<b>${pair} is hitting bollinger band</b>\nPrice at notify: ${price}`;
    bot.sendMessage(channelId, bm, opts);
    bollingerData[pair].alert = true;
  }
};

const simulator = {
  isHavingOrder: false,
  order: {
    pair: "",
    type: "",
    price: 0,
    stoploss: 0,
    takeProfit: 0,
    amount: 0,
  },
  lastOrderTime: 0,
  startingBalance: 100000,
  currentBalance: 100000,
};

const roundTo2 = (number) => {
  return Math.round(number * 100) / 100;
};

const executeLongOrder = (price, threshold, pair) => {
  excuteOrder(price, threshold, pair, "long");
};

const excuteShortOrder = (price, threshold, pair) => {
  excuteOrder(price, threshold, pair, "short");
};

const excuteOrder = (price, threshold, pair, type = "long") => {
  const target = [roundTo2(price - threshold), roundTo2(price + threshold)];
  let stoploss, takeProfit;
  if (type === "long") {
    stoploss = target[0];
    takeProfit = target[1];
  } else {
    stoploss = target[1];
    takeProfit = target[0];
  }
  const amount = roundTo2(simulator.currentBalance / price);
  let bm = `<b>${type.toUpperCase()} SIGNAL DETECTED</b>\nBuying at <b>${price}</b>, stop loss at <b>${stoploss}</b>, take profit at <b>${takeProfit}</b>\nAmount: <b>${amount}</b>`;
  bot.sendMessage(channelId, bm, opts);
  simulator.isHavingOrder = true;
  simulator.lastOrderTime = new Date().getTime();
  simulator.order = {
    type,
    price,
    stoploss,
    takeProfit,
    amount,
    pair,
  };
};

const checkPriceAndBuy = (price, pair) => {
  const bollinger = bollingerData[pair];
  const threshold = (bollinger.middle - bollinger.lower) / 2;
  const actionTime = new Date().getTime() - simulator.lastOrderTime;
  if (
    price <= bollinger.lower - threshold &&
    !simulator.isHavingOrder &&
    actionTime >= 300000
  ) {
    executeLongOrder(price, threshold, pair);
  }
  if (
    price >= bollinger.upper + threshold &&
    !simulator.isHavingOrder &&
    actionTime >= 300000
  ) {
    excuteShortOrder(price, threshold, pair);
  }
  if (simulator.isHavingOrder && simulator.order.pair === pair) {
    checkTarget(simulator.order, price);
  }
};

const calculateProfitLoss = (order, price, type) => {
  let differences = roundTo2(order.amount * (price - order.price));
  if (type === "short") {
    differences = -differences;
  }
  simulator.currentBalance += differences;
  let bm;
  if (differences >= 0) {
    bm = `<b>TAKE PROFIT SUCCESSFUL AT ${price}</b>\nProfit +<b>${differences}</b>`;
  } else {
    bm = `<b>STOP LOSS AT ${price}</b>\nLoss <b>${differences}</b>`;
  }
  bot.sendMessage(channelId, bm, opts);
  simulator.order = {};
  simulator.isHavingOrder = false;
};

const checkTarget = (order, price) => {
  if (
    order.type === "short" &&
    (price <= order.takeProfit || price >= order.stoploss)
  ) {
    calculateProfitLoss(order, price, "short");
  }
  if (
    order.type === "long" &&
    (price >= order.takeProfit || price <= order.stoploss)
  ) {
    calculateProfitLoss(order, price, "long");
  }
};

bot.onText(/\/balance/, (msg) => {
  // 'msg' is the received Message from Telegram
  // 'match' is the result of executing the regexp above on the text content
  // of the message
  const chatId = msg.chat.id;

  // send back the matched "whatever" to the chat
  bot.sendMessage(chatId, simulator.currentBalance);
});

bot.onText(/\/profit/, (msg) => {
  const chatId = msg.chat.id;
  bot.sendMessage(chatId, simulator.currentBalance - simulator.startingBalance);
});

bot.sendMessage(channelId, "Bot started with change strategy");
