const { Client, GatewayIntentBits, EmbedBuilder, SlashCommandBuilder, REST, Routes } = require('discord.js');
const config = require('./config');

class DiscordBot {
  constructor() {
    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
      ],
    });

    this.channelId = config.discord.channelId;
    this.guildId = config.discord.guildId;
    this.isReady = false;
    this.simulator = null; // Will be set by the main bot

    this.setupEventHandlers();
    this.setupCommands();
  }

  /**
   * Setup Discord client event handlers
   */
  setupEventHandlers() {
    this.client.once('ready', () => {
      console.log(`Discord bot logged in as ${this.client.user.tag}`);
      this.isReady = true;
      this.registerSlashCommands();
    });

    this.client.on('interactionCreate', async (interaction) => {
      if (!interaction.isChatInputCommand()) return;

      await this.handleSlashCommand(interaction);
    });

    this.client.on('error', (error) => {
      console.error('Discord client error:', error);
    });
  }

  /**
   * Setup slash commands
   */
  setupCommands() {
    this.commands = [
      new SlashCommandBuilder()
        .setName('balance')
        .setDescription('Get current trading balance'),
      
      new SlashCommandBuilder()
        .setName('profit')
        .setDescription('Get current profit/loss'),
      
      new SlashCommandBuilder()
        .setName('status')
        .setDescription('Get bot status and current positions'),
      
      new SlashCommandBuilder()
        .setName('strategy')
        .setDescription('Get current strategy conditions for a symbol')
        .addStringOption(option =>
          option.setName('symbol')
            .setDescription('Trading pair symbol (e.g., BTCUSDT)')
            .setRequired(true)
        ),
    ];
  }

  /**
   * Register slash commands with Discord
   */
  async registerSlashCommands() {
    try {
      const rest = new REST({ version: '10' }).setToken(config.discord.token);
      
      console.log('Registering Discord slash commands...');
      
      await rest.put(
        Routes.applicationGuildCommands(this.client.user.id, this.guildId),
        { body: this.commands.map(command => command.toJSON()) }
      );
      
      console.log('Discord slash commands registered successfully');
    } catch (error) {
      console.error('Error registering slash commands:', error);
    }
  }

  /**
   * Handle slash command interactions
   */
  async handleSlashCommand(interaction) {
    const { commandName } = interaction;

    try {
      switch (commandName) {
        case 'balance':
          await this.handleBalanceCommand(interaction);
          break;
        case 'profit':
          await this.handleProfitCommand(interaction);
          break;
        case 'status':
          await this.handleStatusCommand(interaction);
          break;
        case 'strategy':
          await this.handleStrategyCommand(interaction);
          break;
        default:
          await interaction.reply('Unknown command');
      }
    } catch (error) {
      console.error('Error handling slash command:', error);
      await interaction.reply('An error occurred while processing the command');
    }
  }

  /**
   * Handle balance command
   */
  async handleBalanceCommand(interaction) {
    if (!this.simulator) {
      await interaction.reply('Simulator not initialized');
      return;
    }

    const balance = this.simulator.currentBalance.toFixed(2);
    await interaction.reply(`💰 Current Balance: $${balance}`);
  }

  /**
   * Handle profit command
   */
  async handleProfitCommand(interaction) {
    if (!this.simulator) {
      await interaction.reply('Simulator not initialized');
      return;
    }

    const profit = (this.simulator.currentBalance - this.simulator.startingBalance).toFixed(2);
    const profitPercent = ((profit / this.simulator.startingBalance) * 100).toFixed(2);
    const emoji = profit >= 0 ? '📈' : '📉';
    
    await interaction.reply(`${emoji} Profit/Loss: $${profit} (${profitPercent}%)`);
  }

  /**
   * Handle status command
   */
  async handleStatusCommand(interaction) {
    if (!this.simulator) {
      await interaction.reply('Simulator not initialized');
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle('🤖 Bot Status')
      .setColor(this.simulator.isHavingOrder ? 0xFF6B6B : 0x4ECDC4)
      .addFields(
        { name: 'Balance', value: `$${this.simulator.currentBalance.toFixed(2)}`, inline: true },
        { name: 'Starting Balance', value: `$${this.simulator.startingBalance.toFixed(2)}`, inline: true },
        { name: 'P&L', value: `$${(this.simulator.currentBalance - this.simulator.startingBalance).toFixed(2)}`, inline: true },
        { name: 'Active Order', value: this.simulator.isHavingOrder ? 'Yes' : 'No', inline: true }
      )
      .setTimestamp();

    if (this.simulator.isHavingOrder && this.simulator.order) {
      const order = this.simulator.order;
      embed.addFields(
        { name: 'Order Type', value: order.type.toUpperCase(), inline: true },
        { name: 'Symbol', value: order.pair, inline: true },
        { name: 'Entry Price', value: `$${order.price}`, inline: true },
        { name: 'Stop Loss', value: `$${order.stoploss}`, inline: true },
        { name: 'Take Profit', value: `$${order.takeProfit}`, inline: true },
        { name: 'Amount', value: order.amount.toString(), inline: true }
      );
    }

    await interaction.reply({ embeds: [embed] });
  }

  /**
   * Handle strategy command
   */
  async handleStrategyCommand(interaction) {
    const symbol = interaction.options.getString('symbol').toUpperCase();
    
    // This would need to be connected to the strategy analysis
    // For now, just acknowledge the command
    await interaction.reply(`📊 Strategy analysis for ${symbol} - Feature coming soon!`);
  }

  /**
   * Send a notification message to the configured channel
   */
  async sendNotification(message, embed = null) {
    if (!this.isReady || !this.channelId) {
      console.log('Discord bot not ready or channel not configured');
      return;
    }

    try {
      const channel = await this.client.channels.fetch(this.channelId);
      
      if (embed) {
        await channel.send({ content: message, embeds: [embed] });
      } else {
        await channel.send(message);
      }
    } catch (error) {
      console.error('Error sending Discord notification:', error);
    }
  }

  /**
   * Send trading signal notification
   */
  async sendTradingSignal(signalData) {
    const { type, symbol, price, stoploss, takeProfit, amount, conditions } = signalData;
    
    const embed = new EmbedBuilder()
      .setTitle(`🚨 ${type.toUpperCase()} SIGNAL DETECTED`)
      .setColor(type === 'BUY' ? 0x00FF00 : 0xFF0000)
      .addFields(
        { name: 'Symbol', value: symbol, inline: true },
        { name: 'Entry Price', value: `$${price}`, inline: true },
        { name: 'Amount', value: amount.toString(), inline: true },
        { name: 'Stop Loss', value: `$${stoploss}`, inline: true },
        { name: 'Take Profit', value: `$${takeProfit}`, inline: true },
        { name: '\u200B', value: '\u200B', inline: true }
      )
      .setTimestamp();

    if (conditions) {
      let conditionsText = '';
      Object.entries(conditions).forEach(([key, condition]) => {
        if (key !== 'allConditionsMet' && key !== 'signal') {
          const emoji = condition.met ? '✅' : '❌';
          conditionsText += `${emoji} ${condition.description}\n`;
        }
      });
      
      if (conditionsText) {
        embed.addFields({ name: 'Strategy Conditions', value: conditionsText });
      }
    }

    await this.sendNotification('', embed);
  }

  /**
   * Send price alert notification
   */
  async sendPriceAlert(symbol, price, alertType) {
    const embed = new EmbedBuilder()
      .setTitle(`📊 ${symbol} Price Alert`)
      .setColor(0xFFAA00)
      .addFields(
        { name: 'Symbol', value: symbol, inline: true },
        { name: 'Current Price', value: `$${price}`, inline: true },
        { name: 'Alert Type', value: alertType, inline: true }
      )
      .setTimestamp();

    await this.sendNotification('', embed);
  }

  /**
   * Send profit/loss notification
   */
  async sendProfitLossNotification(orderData, currentPrice, profit) {
    const { type, symbol, price: entryPrice } = orderData;
    const isProfit = profit >= 0;
    
    const embed = new EmbedBuilder()
      .setTitle(isProfit ? '💰 TAKE PROFIT SUCCESSFUL' : '🛑 STOP LOSS TRIGGERED')
      .setColor(isProfit ? 0x00FF00 : 0xFF0000)
      .addFields(
        { name: 'Symbol', value: symbol, inline: true },
        { name: 'Order Type', value: type.toUpperCase(), inline: true },
        { name: 'Entry Price', value: `$${entryPrice}`, inline: true },
        { name: 'Exit Price', value: `$${currentPrice}`, inline: true },
        { name: 'P&L', value: `$${profit.toFixed(2)}`, inline: true },
        { name: '\u200B', value: '\u200B', inline: true }
      )
      .setTimestamp();

    await this.sendNotification('', embed);
  }

  /**
   * Connect to Discord
   */
  async connect() {
    try {
      await this.client.login(config.discord.token);
    } catch (error) {
      console.error('Error connecting to Discord:', error);
      throw error;
    }
  }

  /**
   * Disconnect from Discord
   */
  async disconnect() {
    if (this.client) {
      await this.client.destroy();
    }
  }

  /**
   * Set simulator reference for commands
   */
  setSimulator(simulator) {
    this.simulator = simulator;
  }
}

module.exports = DiscordBot;
